// a tool box for debug

#include "cmdline.h"
// #include "dbg.h"
#include <juno_pack/DAFragment.h>
#include <juno_pack/DPFragment.h>
#include <juno_pack/Event.h>
#include <juno_pack/Header.h>
#include <juno_pack/JUNOGCUPacket_dummy.h>
#include <sys/types.h>
#include <cassert>
#include <cstddef>
#include <cstdint>
#include <cstdlib>
#include <elf.h>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <limits>
#include <map>
#include <memory>
#include <sstream>
#include <string>
#include <string_view>
#include <utility>
#include <vector>

constexpr int kFileHeadSize = 256;
constexpr int kMaxDataSize  = (1024 * 1024 * 1024);  // 1GB

const std::map<uint32_t, std::string_view> gEventTypeName = {
    {0x11, "CD-LPMT waveform ev"}, {0x12, "WP-LPMT waveform ev"},
    {0x13, "SPMT t/q ev"},         {0x14, "TT ev"},
    {0x15, "CD t/q ev"},           {0x16, "WP t/q ev"},
    {0x17, "MM t/q ev"},           {0xff, "Unknow ev"},
};
const std::map<uint16_t, std::string_view> gEvDataListName = {
    {0x3100, "OEC reconstructed data"},
    {0x2001, "Global trigger data"},
    {0x2003, "Veto trigger data"},
    {0x2005, "MM data"},
    {0x2100, "Software trigger data"},
    {0x1101, "CD t/q"},
    {0x1102, "SPMT t/q"},
    {0x1103, "VETO t/q"},
    {0x1204, "TT data"},
    {0x1001, "CD waveform"},
    {0x1003, "VETO waveform"},
    {0x3000, "OEC reconstructed t/q"},
};
const std::map<junopack::data_type, std::string_view> gDataTypeName = {
    {junopack::data_type::kWaveform, "waveform"},
    {junopack::data_type::kOecRecTq, "OEC reconstructed t/q"},
    {junopack::data_type::kOecRecResult, "OEC reconstructed data"},
    {junopack::data_type::kTq, "t/q"},
    // {junopack::data_type::TT, "TT data"},
    {junopack::data_type::kHardTrigger, "trigger data"},
    {junopack::data_type::kHardTrigger, "trigger data"},
    {junopack::data_type::kHardTrigger, "MM trigger data"},
    {junopack::data_type::kSoftTrigger, "software trigger data"},
};

void dump(char* head) {
    junopack::dump(reinterpret_cast<uint8_t*>(head), junopack::read::Header::gCommonSize);
}
void bye_bye() {
    std::cout << "no work to do. see you next time ^_^" << std::endl;
    exit(EXIT_SUCCESS);
}

/*
void parse_da_fragment(void* ptr) {
    junopack::DAFragment da_header(reinterpret_cast<uint8_t*>(ptr), true);
    // todo
}
void parse_da_fragment(cmdline::parser& parser) {
    std::string filename = parser.get<std::string>("filename");
    std::ifstream file(filename, std::ios::binary);
    auto begin = file.tellg();
    file.seekg(0, std::ios::end);
    auto end = file.tellg();
    std::cout << "The file size = " << (end - begin) << std::endl;
    file.seekg(0, std::ios::beg);

    // read da fragment
    char head[junoread::Header::s_commonHeaderSize + 1];
    char buffer[kDataSize];
    while (file.eof()) {
        if (file.read(head, junoread::Header::s_commonHeaderSize)) {
            junoread::Header h(reinterpret_cast<uint8_t*>(head));

            if (h.startMarker() != junoread::start_marker_t::MARKER_DA) {
                auto position = file.tellg();
                std::cout << "Find a marker error in file " << filename << std::endl;
                std::cout << "error position is "
                          << (position - begin - junoread::Header::s_commonHeaderSize)
                          << "\ndump:" << std::endl;

                dump(head);
                exit(EXIT_FAILURE);
            }
            size_t totalsize = h.totalSize();
            if (totalsize > kDataSize) {
                std::cout << "buffer size is insufficient, please large it" << std::endl;
                std::cout << "totalsize = " << totalsize << std::endl;
                exit(EXIT_FAILURE);
            }
            file.seekg(-junoread::Header::s_commonHeaderSize, std::ios::cur);
            if (file.read(buffer, totalsize))
                parse_da_fragment(buffer);
            else {
                auto position = file.tellg();
                std::cerr << "cannot read file error"
                          << "error position is " << (position - begin) << std::endl;
                exit(EXIT_FAILURE);
            }
        } else {
            auto position = file.tellg();
            std::cerr << "cannot read file error"
                      << "error position is " << (position - begin) << std::endl;
            exit(EXIT_FAILURE);
        }
    }
}*/
void print_ev_info(junopack::read::Event& ev, bool detail = false) {
    if (detail) {
        std::cout << "detail msg" << std::endl;
        uint32_t ev_type = ev.evTag() >> 16;
        auto ret         = gEventTypeName.find(ev_type);
        std::string_view ev_type_name;
        if (ret != gEventTypeName.end())
            ev_type_name = ret->second;
        else
            ev_type_name = gEventTypeName.at(0xff);

        std::cout << "time-fragment id: " << ev.l1id() << "\n"
                  << "ev id: " << ev.evId() << "\n"
                  << "ev time: " << ev.evTime() << " (sec: " << ev.evSecTime()
                  << ", nano: " << ev.evNanoTime() << ")\n"
                  << "ev type: 0x" << std::hex << std::setw(4) << std::setfill('0')
                  << ev_type << " (" << ev_type_name << ")\n"
                  << std::dec << std::resetiosflags(std::ios_base::basefield)
                  << "data list: " << std::endl;

        auto data_list = ev.getDataFrags();
        if (data_list.empty()) {
            std::cout << "empty event" << std::endl;
            return;
        }
        for (size_t index = 0; index < data_list.size(); ++index) {
            if (!data_list[index]) continue;
            std::cout << index << "> "
                      << gEvDataListName.at(data_list[index]->header_ptr->channel_tag)
                      << ", data size: "
                      << data_list[index]->header_ptr->data_size_low_32bit +
                             (static_cast<size_t>(
                                  data_list[index]->header_ptr->data_size_high_16bit)
                              << 32)
                      << std::endl;
        }
        while (true) {
            std::cout << "Which type data do you want to see?(type number 0 ~ "
                      << data_list.size() - 1 << " or type e to exit): ";
            std::string select;
            std::cin >> std::ws >> select;
            if (select.empty()) continue;
            if (select[0] == 'e') break;
            size_t data_id = std::stoull(select);
            if (data_id > data_list.size()) continue;
            if (data_list[data_id]) {
                std::cout << gEvDataListName.at(
                                 data_list[data_id]->header_ptr->channel_tag)
                          << ": \n";
                const uint8_t* ptr     = data_list[data_id]->datas.first;
                const uint8_t* ptr_end = data_list[data_id]->datas.second;
                while (true) {
                    size_t print_byte = 160;
                    if (ptr < ptr_end) {
                        std::cout << "connitue print?(Byte_num or n)";
                        std::string print_flag;
                        std::cin >> std::ws >> print_flag;
                        if (print_flag.empty()) continue;
                        if (print_flag[0] == 'y') continue;
                        if (print_flag[0] == 'n') break;
                        try {
                            print_byte = std::stoull(print_flag);
                        } catch (...) {
                            std::cerr << "input format err, please type number or n or y"
                                      << std::endl;
                            continue;
                        }
                    } else
                        break;

                    if (print_byte > ptr_end - ptr) print_byte = ptr_end - ptr;
                    junopack::dump(ptr, print_byte);
                    ptr += print_byte;
                }
            } else
                std::cout << "no data" << std::endl;
        }
    } else
        std::cout << "ev-" << ev.evId() << " time: " << ev.evSecTime() << "."
                  << ev.evNanoTime() << " data size: " << ev.dataSize()
                  << " stream tag: 0x" << std::hex << std::setw(8) << std::setfill('0')
                  << ev.evTag() << std::dec
                  << std::resetiosflags(std::ios_base::basefield) << std::endl;
}
void parse_evs_data(cmdline::parser& parser) {
    std::string filename = parser.get<std::string>("filename");
    std::ifstream file(filename, std::ios::binary);

    auto begin = file.tellg();
    file.seekg(0, std::ios::end);
    auto end = file.tellg();
    std::cout << "The file size = " << (end - begin) << std::endl;
    file.seekg(0, std::ios::beg);

    // read evs
    char head[junopack::read::Header::gCommonSize];
    std::vector<std::unique_ptr<char[]>> evs_buffer;
    while (!file.eof()) {
        auto ev_header_pos = file.tellg();
        if (file.read(head, junopack::read::Header::gCommonSize)) {
            junopack::read::Header h(reinterpret_cast<uint8_t*>(head), true);

            if (!h.checkFormat()) exit(EXIT_FAILURE);

            if (h.startMarker() != junopack::start_marker_t::MARKER_EVENT) {
                auto position = file.tellg();
                std::cerr << "Find a marker error in file " << filename << std::endl;
                std::cerr << "error position is "
                          << (position - begin - junopack::read::Header::gCommonSize)
                          << "\ndump:" << std::endl;
                dump(head);
                exit(EXIT_FAILURE);
            }
            size_t totalsize = h.totalSize();
            if (totalsize > kMaxDataSize) {
                std::cout << "event totalsize is too big (" << totalsize / 1024 / 1024
                          << "MiB) > " << kMaxDataSize / 1024 / 1024
                          << "MiB; please check it." << std::endl;
                exit(EXIT_FAILURE);
            }
            auto buffer = std::make_unique<char[]>(totalsize);
            file.seekg(-static_cast<int>(junopack::read::Header::gCommonSize),
                       std::ios::cur);
            auto ev_pos = file.tellg();
            if (!file.read(buffer.get(), totalsize)) {
                std::cerr << "read event format error: "
                          << "error position is " << (ev_pos - begin)
                          << ", total size of dp frag is " << totalsize << std::endl;

                exit(EXIT_FAILURE);
            }
            evs_buffer.push_back(std::move(buffer));
        } else {
            if (file.eof()) break;
            std::cerr << "read dp header error: "
                      << "error position is " << (ev_header_pos - begin) << std::endl;
            exit(EXIT_FAILURE);
        }
    }
    file.close();
    // parse ev
    std::cout << "There are " << evs_buffer.size() << " event " << std::endl;
    if (evs_buffer.empty()) bye_bye();

    std::vector<junopack::read::Event> events;
    events.reserve(evs_buffer.size());
    for (auto& buf : evs_buffer) {
        events.emplace_back(buf.get(), true);
    }
    while (true) {
        std::cout << "Which ev do you want to see?(type number(0 ~ " << events.size() - 1
                  << ") or type e for exit):";
        std::string select;
        std::stringstream ss;
        size_t ev_id;
        std::cin >> std::ws >> select;
        if (select.empty()) continue;
        if (select[0] == 'e') break;
        ss << select;
        if (ss >> ev_id)
            if (ev_id < events.size()) print_ev_info(events[ev_id], true);
    }
    bye_bye();
}
void parse_dp_fragment(void* ptr) {
    junopack::read::DPFragment dp_header(ptr, true);

    bool check_flag        = dp_header.checkFormat();
    junopack::TFID_t tf_id = dp_header.data_assemble_id();  // time fragment id
    uint32_t ev_num        = dp_header.eventSize();
    if (!check_flag) {
        std::cerr << "dp fragment format error" << std::endl;
        std::cout << "please check your file" << std::endl;
        exit(EXIT_FAILURE);
    }
    std::vector<junopack::read::Event> evs;
    const uint8_t* ptr2ev  = dp_header.data();
    const uint8_t* ptr2end = ptr2ev + dp_header.dataSize();
    while (ptr2ev < ptr2end) {
        evs.emplace_back(ptr2ev, true);
        ptr2ev += evs.back().totalSize();
    }
    if (evs.size() != ev_num) {
        std::cout << "[L1ID-{" << tf_id << "}] ev num no correct" << std::endl;
    }

    std::cout << "time-fragment id = " << tf_id << ", there are " << ev_num << " events"
              << std::endl;
    if (ev_num == 0) return;
    for (auto& ev : evs) {
        print_ev_info(ev);
    }
    while (true) {
        std::cout << "Which ev do you want to see?(type number(0 ~ " << ev_num - 1
                  << ") or type e for exit):";
        std::string select;
        std::stringstream ss;
        size_t ev_id;
        std::cin >> std::ws >> select;
        if (select.empty()) continue;
        if (select[0] == 'e') break;
        ss << select;
        if (ss >> ev_id)
            if (ev_id < ev_num) print_ev_info(evs[ev_id], true);
    }
}

void get_ev_from_dp_by_range(void* ptr, std::vector<junopack::read::Event>& evs,
                             std::pair<uint32_t, uint32_t> ev_size_range) {
    junopack::read::DPFragment dp_header(reinterpret_cast<uint8_t*>(ptr), true);

    bool check_flag = dp_header.checkFormat();
    uint32_t tf_id  = dp_header.data_assemble_id();  // time fragment id
    uint32_t ev_num = dp_header.eventSize();
    if (!check_flag) {
        std::cerr << "dp fragment format error" << std::endl;
        std::cout << "please check your file" << std::endl;
        exit(EXIT_FAILURE);
    }
    const uint8_t* ptr2ev  = dp_header.data();
    const uint8_t* ptr2end = ptr2ev + dp_header.dataSize();
    while (ptr2ev < ptr2end) {
        evs.emplace_back(ptr2ev, true);
        ptr2ev += evs.back().totalSize();
        auto data_size = evs.back().dataSize();
        if (ev_size_range.second <= data_size || data_size < ev_size_range.first)
            evs.pop_back();
    }
}
/*
void parse_dp_fragment(cmdline::parser& parser) {
    std::string filename = parser.get<std::string>("filename");
    std::ifstream file(filename, std::ios::binary);
    auto begin = file.tellg();
    file.seekg(0, std::ios::end);
    auto end = file.tellg();
    std::cout << "The file size = " << (end - begin) << std::endl;
    file.seekg(0, std::ios::beg);

    // read da fragment
    char head[junoread::Header::s_commonHeaderSize + 1];
    char buffer[kDataSize];
    while (file.eof()) {
        if (file.read(head, junoread::Header::s_commonHeaderSize)) {
            junoread::Header h(reinterpret_cast<uint8_t*>(head));

            if (h.startMarker() != junoread::start_marker_t::MARKER_DP) {
                auto position = file.tellg();
                std::cerr << "Find a marker error in file " << filename << std::endl;
                std::cerr << "error position is "
                          << (position - begin - junoread::Header::s_commonHeaderSize)
                          << "\ndump:" << std::endl;

                dump(head);
                exit(EXIT_FAILURE);
            }
            size_t totalsize = h.totalSize();
            if (totalsize > kDataSize) {
                std::cout << "buffer size is insufficient, please large it" << std::endl;
                std::cout << "totalsize = " << totalsize << std::endl;
                exit(EXIT_FAILURE);
            }
            file.seekg(-junoread::Header::s_commonHeaderSize, std::ios::cur);
            if (file.read(buffer, totalsize))
                parse_dp_fragment(buffer);
            else {
                auto position = file.tellg();
                std::cerr << "cannot read file error"
                          << "error position is " << (position - begin) << std::endl;
                exit(EXIT_FAILURE);
            }
        } else {
            auto position = file.tellg();
            std::cerr << "cannot read file error"
                      << "error position is " << (position - begin) << std::endl;
            exit(EXIT_FAILURE);
        }
    }
}
*/
void parse_es_output_file(cmdline::parser& parser) {
    std::string filename = parser.get<std::string>("filename");
    std::ifstream file(filename, std::ios::binary);

    auto begin = file.tellg();
    file.seekg(0, std::ios::end);
    auto end = file.tellg();
    std::cout << "The file size = " << (end - begin) << std::endl;
    file.seekg(0, std::ios::beg);

    // char head[file_head_size+1];
    // file.read(head,file_head_size);
    file.seekg(kFileHeadSize, std::ios::beg);

    // read da fragment
    char head[junopack::read::Header::gCommonSize];
    std::vector<std::unique_ptr<char[]>> dps;
    while (!file.eof()) {
        auto dp_header_pos = file.tellg();
        if (file.read(head, junopack::read::Header::gCommonSize)) {
            junopack::read::Header h(reinterpret_cast<uint8_t*>(head), true);

            if (!h.checkFormat()) exit(EXIT_FAILURE);

            if (h.startMarker() != junopack::start_marker_t::MARKER_DP) {
                auto position = file.tellg();
                std::cerr << "Find a marker error in file " << filename << std::endl;
                std::cerr << "error position is "
                          << (position - begin - junopack::read::Header::gCommonSize)
                          << "\ndump:" << std::endl;
                dump(head);
                exit(EXIT_FAILURE);
            }
            size_t totalsize = h.totalSize();
            if (totalsize > kMaxDataSize) {
                std::cout << "dp totalsize is too big (" << totalsize / 1024 / 1024
                          << "MiB) > " << kMaxDataSize / 1024 / 1024
                          << "MiB; please check it." << std::endl;
                exit(EXIT_FAILURE);
            }
            auto buffer = std::make_unique<char[]>(totalsize);
            file.seekg(-static_cast<int>(junopack::read::Header::gCommonSize),
                       std::ios::cur);
            auto dp_pos = file.tellg();
            if (!file.read(buffer.get(), totalsize)) {
                std::cerr << "read dp fragment error: "
                          << "error position is " << (dp_pos - begin)
                          << ", total size of dp frag is " << totalsize << std::endl;

                exit(EXIT_FAILURE);
            }
            dps.push_back(std::move(buffer));
        } else {
            if (file.eof()) break;
            std::cerr << "read dp header error: "
                      << "error position is " << (dp_header_pos - begin) << std::endl;
            exit(EXIT_FAILURE);
        }
    }
    file.close();
    // parse dp
    std::cout << "There are " << dps.size() << " dp fragment" << std::endl;
    if (dps.empty()) bye_bye();
    int left_boundary  = parser.get<int>("left_boundary");
    int right_boundary = parser.get<int>("right_boundary");
    if (left_boundary == 0 && right_boundary == 0)
        while (true) {
            std::cout << "which dp fragment you want to see?(please type 0 ~ "
                      << dps.size() - 1 << ")";
            std::string data;
            size_t dp_id;
            std::cin >> std::ws >> data;
            std::stringstream ss(data);
            if (ss >> dp_id)
                if (dp_id < dps.size()) {
                    std::cout << "parse dp " << dp_id << ": ";
                    parse_dp_fragment(dps[dp_id].get());
                    std::string select;
                    while (true) {
                        std::cout << "continue?(y/n)";
                        std::cin >> std::ws >> select;
                        if (select.empty()) continue;
                        if (select[0] == 'y') break;
                        if (select[0] == 'n') bye_bye();
                    }
                }
        }
    else {
        int left;
        int right;
        std::vector<junopack::read::Event> evs;
        if (left_boundary < right_boundary) {
            left  = left_boundary;
            right = right_boundary;
            std::cout << "search ev, which size is in range: (" << left << "," << right
                      << ")" << std::endl;
            for (auto& dp : dps) {
                get_ev_from_dp_by_range(dp.get(), evs, {left, right});
            }

        } else {
            left  = 0;
            right = right_boundary;
            std::cout << "search ev, which size is in range: (" << left << "," << right
                      << ")" << std::endl;
            for (auto& dp : dps) {
                get_ev_from_dp_by_range(dp.get(), evs, {left, right});
            }

            left  = left_boundary;
            right = std::numeric_limits<int>::max();
            std::cout << "search ev, which size is in range: [" << left << "," << right
                      << ")" << std::endl;
            for (auto& dp : dps) {
                get_ev_from_dp_by_range(dp.get(), evs, {left, right});
            }
        }

        std::cout << "There are " << evs.size() << " evs' data size is in range"
                  << std::endl;

        if (evs.empty()) bye_bye();
        // for (auto& ev : evs) print_ev_info(ev);
        while (true) {
            std::cout << "Which ev do you want to see?(type number(0 ~ " << evs.size() - 1
                      << ") or type e for exit, l for list):" << std::endl;
            std::string select;
            std::stringstream ss;
            size_t ev_id;
            std::cin >> std::ws >> select;
            if (select.empty()) continue;
            if (select[0] == 'e') break;
            if (select[0] == 'l')
                for (auto& ev : evs) print_ev_info(ev);
            ss << select;
            if (ss >> ev_id)
                if (ev_id < evs.size()) print_ev_info(evs[ev_id], true);
        }
        evs.clear();
        bye_bye();
    }
}

int main(int argc, char* argv[]) {
    cmdline::parser parser;
    parser.add<std::string>("filename", 'f', "file name to be parsed", true, "");
    parser.add<int>("left_boundary", 'l', "filter, ev size is (left value, inf)", false,
                    0);
    parser.add<int>("right_boundary", 'r', "filter, ev size is (0, right value)", false,
                    0);
    // parser.add("format", 'c', "check format ON");
    // parser.add("transfer", 't', "transfer event data to raw data format");

    parser.parse_check(argc, argv);

    std::string filename = parser.get<std::string>("filename");
    std::cout << "JUNO PACK VERSION: " << JUNOPACK_VERSION_STR << std::endl;
    std::cout << "*******************open file : " << filename << "*******************"
              << std::endl;

    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) std::cout << "cannot open this file : " << filename << std::endl;
    char file_head[kFileHeadSize];
    file.read(file_head, kFileHeadSize);
    file.close();
    auto* data_ptr             = reinterpret_cast<uint32_t*>(file_head);
    uint32_t juno_pack_version = *(data_ptr + 1);

    switch (*data_ptr) {
        case junopack::start_marker_t::MARKER_DP:  // 解析DP数据
            std::cout << "Finding DP fragment head" << std::endl;
            break;
        case junopack::start_marker_t::MARKER_DA:  // 解析DA数据
            std::cout << "Finding DA fragment head" << std::endl;
            break;
        case junopack::JUNOGCUPacket_dummy::header_marker:  // 解析波形数据
            std::cout << "Finding raw wave fragment" << std::endl;
            break;
        case kFileHeadSize:  // 解析es 存盘文件
            std::cout << "Finding ES output file head" << std::endl;
            if (juno_pack_version != JUNOPACK_VERSION) {
                std::cout << "file's pack version is " << juno_pack_version << std::endl;
                std::cout << "this program cannot parse the file!!!" << std::endl;
                exit(EXIT_FAILURE);
            }
            parse_es_output_file(parser);
            break;
        case junopack::start_marker_t::MARKER_EVENT:  // 解析Events数据
            std::cout << "Finding Event head" << std::endl;
            parse_evs_data(parser);
            break;
        default:
            std::cout << "file format is wrong, cannot match any format." << std::endl;
            std::cout << "please check your input file." << std::endl;
            break;
    }
}