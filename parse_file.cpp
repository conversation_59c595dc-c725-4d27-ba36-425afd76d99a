

void parse_es_output_file(cmdline::parser& parser) {
    std::string filename = parser.get<std::string>("filename");
    std::ifstream file(filename, std::ios::binary);

    auto begin = file.tellg();
    file.seekg(0, std::ios::end);
    auto end = file.tellg();
    std::cout << "The file size = " << (end - begin) << std::endl;
    file.seekg(0, std::ios::beg);

    // char head[file_head_size+1];
    // file.read(head,file_head_size);
    file.seekg(kFileHeadSize, std::ios::beg);

    // read da fragment
    char head[junopack::read::Header::gCommonSize];
    std::vector<std::unique_ptr<char[]>> dps;
    while (!file.eof()) {
        auto dp_header_pos = file.tellg();
        if (file.read(head, junopack::read::Header::gCommonSize)) {
            junopack::read::Header h(reinterpret_cast<uint8_t*>(head), true);

            if (!h.checkFormat()) exit(EXIT_FAILURE);

            if (h.startMarker() != junopack::start_marker_t::MARKER_DP) {
                auto position = file.tellg();
                std::cerr << "Find a marker error in file " << filename << std::endl;
                std::cerr << "error position is "
                          << (position - begin - junopack::read::Header::gCommonSize)
                          << "\ndump:" << std::endl;
                dump(head);
                exit(EXIT_FAILURE);
            }
            size_t totalsize = h.totalSize();
            if (totalsize > kMaxDataSize) {
                std::cout << "dp totalsize is too big (" << totalsize / 1024 / 1024
                          << "MiB) > " << kMaxDataSize / 1024 / 1024
                          << "MiB; please check it." << std::endl;
                exit(EXIT_FAILURE);
            }
            auto buffer = std::make_unique<char[]>(totalsize);
            file.seekg(-static_cast<int>(junopack::read::Header::gCommonSize),
                       std::ios::cur);
            auto dp_pos = file.tellg();
            if (!file.read(buffer.get(), totalsize)) {
                std::cerr << "read dp fragment error: "
                          << "error position is " << (dp_pos - begin)
                          << ", total size of dp frag is " << totalsize << std::endl;

                exit(EXIT_FAILURE);
            }
            dps.push_back(std::move(buffer));
        } else {
            if (file.eof()) break;
            std::cerr << "read dp header error: "
                      << "error position is " << (dp_header_pos - begin) << std::endl;
            exit(EXIT_FAILURE);
        }
    }
    file.close();
    // parse dp
    std::cout << "There are " << dps.size() << " dp fragment" << std::endl;
    if (dps.empty()) bye_bye();
    int left_boundary  = parser.get<int>("left_boundary");
    int right_boundary = parser.get<int>("right_boundary");
    if (left_boundary == 0 && right_boundary == 0)
        while (true) {
            std::cout << "which dp fragment you want to see?(please type 0 ~ "
                      << dps.size() - 1 << ")";
            std::string data;
            size_t dp_id;
            std::cin >> std::ws >> data;
            std::stringstream ss(data);
            if (ss >> dp_id)
                if (dp_id < dps.size()) {
                    std::cout << "parse dp " << dp_id << ": ";
                    parse_dp_fragment(dps[dp_id].get());
                    std::string select;
                    while (true) {
                        std::cout << "continue?(y/n)";
                        std::cin >> std::ws >> select;
                        if (select.empty()) continue;
                        if (select[0] == 'y') break;
                        if (select[0] == 'n') bye_bye();
                    }
                }
        }
    else {
        int left;
        int right;
        std::vector<junopack::read::Event> evs;
        if (left_boundary < right_boundary) {
            left  = left_boundary;
            right = right_boundary;
            std::cout << "search ev, which size is in range: (" << left << "," << right
                      << ")" << std::endl;
            for (auto& dp : dps) {
                get_ev_from_dp_by_range(dp.get(), evs, {left, right});
            }

        } else {
            left  = 0;
            right = right_boundary;
            std::cout << "search ev, which size is in range: (" << left << "," << right
                      << ")" << std::endl;
            for (auto& dp : dps) {
                get_ev_from_dp_by_range(dp.get(), evs, {left, right});
            }

            left  = left_boundary;
            right = std::numeric_limits<int>::max();
            std::cout << "search ev, which size is in range: [" << left << "," << right
                      << ")" << std::endl;
            for (auto& dp : dps) {
                get_ev_from_dp_by_range(dp.get(), evs, {left, right});
            }
        }

        std::cout << "There are " << evs.size() << " evs' data size is in range"
                  << std::endl;

        if (evs.empty()) bye_bye();
        // for (auto& ev : evs) print_ev_info(ev);
        while (true) {
            std::cout << "Which ev do you want to see?(type number(0 ~ " << evs.size() - 1
                      << ") or type e for exit, l for list):" << std::endl;
            std::string select;
            std::stringstream ss;
            size_t ev_id;
            std::cin >> std::ws >> select;
            if (select.empty()) continue;
            if (select[0] == 'e') break;
            if (select[0] == 'l')
                for (auto& ev : evs) print_ev_info(ev);
            ss << select;
            if (ss >> ev_id)
                if (ev_id < evs.size()) print_ev_info(evs[ev_id], true);
        }
        evs.clear();
    }
}

